/**
 * Frontend timestamp utility functions for handling Unix timestamps
 * This module provides consistent timestamp handling across the frontend application
 */

/**
 * Convert a JavaScript Date object to Unix timestamp (seconds since epoch)
 */
export function dateToUnixTimestamp(date: Date): number {
  return Math.floor(date.getTime() / 1000);
}

/**
 * Convert Unix timestamp (seconds since epoch) to JavaScript Date object
 */
export function unixTimestampToDate(timestamp: number): Date {
  return new Date(timestamp * 1000);
}

/**
 * Get current Unix timestamp
 */
export function getCurrentUnixTimestamp(): number {
  return dateToUnixTimestamp(new Date());
}

/**
 * Format Unix timestamp for display
 */
export function formatTimestamp(timestamp: number, options?: Intl.DateTimeFormatOptions): string {
  const date = unixTimestampToDate(timestamp);
  return date.toLocaleDateString('en-US', options);
}

/**
 * Format Unix timestamp as time
 */
export function formatTime(timestamp: number, options?: Intl.DateTimeFormatOptions): string {
  const date = unixTimestampToDate(timestamp);
  return date.toLocaleTimeString('en-US', options);
}

/**
 * Format Unix timestamp as date and time
 */
export function formatDateTime(timestamp: number, dateOptions?: Intl.DateTimeFormatOptions, timeOptions?: Intl.DateTimeFormatOptions): string {
  const date = unixTimestampToDate(timestamp);
  const formattedDate = date.toLocaleDateString('en-US', dateOptions);
  const formattedTime = date.toLocaleTimeString('en-US', timeOptions);
  return `${formattedDate} ${formattedTime}`;
}

/**
 * Format Unix timestamp for card display (e.g., "Mon, Dec 25")
 */
export function formatCardDate(timestamp: number): string {
  return formatTimestamp(timestamp, {
    weekday: 'short',
    month: 'short',
    day: 'numeric'
  });
}

/**
 * Format Unix timestamp for date added display (e.g., "Dec 25, 2023")
 */
export function formatDateAdded(timestamp: number): string {
  return formatTimestamp(timestamp, {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  });
}

/**
 * Format Unix timestamp for schedule display (e.g., "Monday, December 25")
 */
export function formatScheduleDate(timestamp: number): string {
  return formatTimestamp(timestamp, {
    weekday: 'long',
    month: 'long',
    day: 'numeric'
  });
}

/**
 * Validate Unix timestamp
 */
export function isValidUnixTimestamp(timestamp: number): boolean {
  // Check if it's a number and within reasonable bounds
  const now = getCurrentUnixTimestamp();
  const maxFuture = now + (365 * 24 * 60 * 60 * 10); // 10 years in the future
  
  return (
    typeof timestamp === 'number' &&
    !isNaN(timestamp) &&
    timestamp > 0 &&
    timestamp <= maxFuture
  );
}

/**
 * Check if a Unix timestamp represents today
 */
export function isToday(timestamp: number): boolean {
  const date = unixTimestampToDate(timestamp);
  const today = new Date();
  
  return (
    date.getFullYear() === today.getFullYear() &&
    date.getMonth() === today.getMonth() &&
    date.getDate() === today.getDate()
  );
}

/**
 * Check if a Unix timestamp represents a past date
 */
export function isPast(timestamp: number): boolean {
  const now = getCurrentUnixTimestamp();
  return timestamp < now;
}

/**
 * Check if a Unix timestamp represents a future date
 */
export function isFuture(timestamp: number): boolean {
  const now = getCurrentUnixTimestamp();
  return timestamp > now;
}

/**
 * Get Unix timestamp for start of day
 */
export function getStartOfDay(timestamp: number): number {
  const date = unixTimestampToDate(timestamp);
  date.setHours(0, 0, 0, 0);
  return dateToUnixTimestamp(date);
}

/**
 * Get Unix timestamp for end of day
 */
export function getEndOfDay(timestamp: number): number {
  const date = unixTimestampToDate(timestamp);
  date.setHours(23, 59, 59, 999);
  return dateToUnixTimestamp(date);
}

/**
 * Add days to Unix timestamp
 */
export function addDays(timestamp: number, days: number): number {
  const date = unixTimestampToDate(timestamp);
  date.setDate(date.getDate() + days);
  return dateToUnixTimestamp(date);
}

/**
 * Subtract days from Unix timestamp
 */
export function subtractDays(timestamp: number, days: number): number {
  return addDays(timestamp, -days);
}

/**
 * Calculate difference between two Unix timestamps in seconds
 */
export function timestampDifference(timestamp1: number, timestamp2: number): number {
  return Math.abs(timestamp1 - timestamp2);
}

/**
 * Calculate difference between two Unix timestamps in days
 */
export function daysDifference(timestamp1: number, timestamp2: number): number {
  return Math.floor(timestampDifference(timestamp1, timestamp2) / (24 * 60 * 60));
}

/**
 * Get relative time string (e.g., "2 days ago", "in 3 hours")
 */
export function getRelativeTime(timestamp: number): string {
  const now = getCurrentUnixTimestamp();
  const diff = timestamp - now;
  const absDiff = Math.abs(diff);
  
  const minute = 60;
  const hour = minute * 60;
  const day = hour * 24;
  const week = day * 7;
  const month = day * 30;
  const year = day * 365;
  
  if (absDiff < minute) {
    return 'just now';
  } else if (absDiff < hour) {
    const minutes = Math.floor(absDiff / minute);
    return diff > 0 ? `in ${minutes} minute${minutes > 1 ? 's' : ''}` : `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
  } else if (absDiff < day) {
    const hours = Math.floor(absDiff / hour);
    return diff > 0 ? `in ${hours} hour${hours > 1 ? 's' : ''}` : `${hours} hour${hours > 1 ? 's' : ''} ago`;
  } else if (absDiff < week) {
    const days = Math.floor(absDiff / day);
    return diff > 0 ? `in ${days} day${days > 1 ? 's' : ''}` : `${days} day${days > 1 ? 's' : ''} ago`;
  } else if (absDiff < month) {
    const weeks = Math.floor(absDiff / week);
    return diff > 0 ? `in ${weeks} week${weeks > 1 ? 's' : ''}` : `${weeks} week${weeks > 1 ? 's' : ''} ago`;
  } else if (absDiff < year) {
    const months = Math.floor(absDiff / month);
    return diff > 0 ? `in ${months} month${months > 1 ? 's' : ''}` : `${months} month${months > 1 ? 's' : ''} ago`;
  } else {
    const years = Math.floor(absDiff / year);
    return diff > 0 ? `in ${years} year${years > 1 ? 's' : ''}` : `${years} year${years > 1 ? 's' : ''} ago`;
  }
}

/**
 * Convert Date object to date string for API requests (YYYY-MM-DD)
 */
export function dateToApiDateString(date: Date): string {
  return date.toISOString().split('T')[0];
}

/**
 * Convert Unix timestamp to date string for API requests (YYYY-MM-DD)
 */
export function timestampToApiDateString(timestamp: number): string {
  return dateToApiDateString(unixTimestampToDate(timestamp));
}

/**
 * Parse API date string (YYYY-MM-DD) to Unix timestamp
 */
export function apiDateStringToTimestamp(dateString: string): number {
  const date = new Date(dateString + 'T00:00:00.000Z');
  return dateToUnixTimestamp(date);
}

/**
 * Get array of Unix timestamps for a date range
 */
export function getDateRange(startTimestamp: number, endTimestamp: number): number[] {
  const dates: number[] = [];
  let current = getStartOfDay(startTimestamp);
  const end = getStartOfDay(endTimestamp);
  
  while (current <= end) {
    dates.push(current);
    current = addDays(current, 1);
  }
  
  return dates;
}

/**
 * Get Unix timestamp for a specific date (year, month, day)
 */
export function getTimestampForDate(year: number, month: number, day: number): number {
  const date = new Date(year, month - 1, day); // month is 0-indexed in Date constructor
  return dateToUnixTimestamp(date);
}
