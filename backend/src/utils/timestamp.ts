/**
 * Timestamp utility functions for converting between database timestamps and Unix timestamps
 * This module provides consistent timestamp handling across the application
 */

/**
 * Convert a JavaScript Date object to Unix timestamp (seconds since epoch)
 */
export function dateToUnixTimestamp(date: Date): number {
  return Math.floor(date.getTime() / 1000);
}

/**
 * Convert Unix timestamp (seconds since epoch) to JavaScript Date object
 */
export function unixTimestampToDate(timestamp: number): Date {
  return new Date(timestamp * 1000);
}

/**
 * Convert PostgreSQL timestamp string to Unix timestamp
 */
export function pgTimestampToUnixTimestamp(pgTimestamp: string): number {
  return dateToUnixTimestamp(new Date(pgTimestamp));
}

/**
 * Convert Unix timestamp to PostgreSQL timestamp string
 */
export function unixTimestampToPgTimestamp(timestamp: number): string {
  return unixTimestampToDate(timestamp).toISOString();
}

/**
 * Get current Unix timestamp
 */
export function getCurrentUnixTimestamp(): number {
  return dateToUnixTimestamp(new Date());
}

/**
 * Convert PostgreSQL date string (YYYY-MM-DD) to Unix timestamp at start of day
 */
export function pgDateToUnixTimestamp(pgDate: string): number {
  const date = new Date(pgDate + 'T00:00:00.000Z');
  return dateToUnixTimestamp(date);
}

/**
 * Convert Unix timestamp to PostgreSQL date string (YYYY-MM-DD)
 */
export function unixTimestampToPgDate(timestamp: number): string {
  return unixTimestampToDate(timestamp).toISOString().split('T')[0];
}

/**
 * Convert database row timestamps to Unix timestamps
 * This function processes common timestamp fields in database rows
 */
export function convertRowTimestamps(row: any): any {
  const converted = { ...row };
  
  // Common timestamp fields to convert
  const timestampFields = [
    'created_at',
    'updated_at',
    'last_login_at',
    'last_accessed_at',
    'worn_at',
    'cached_at',
    'expires_at'
  ];
  
  // Common date fields to convert
  const dateFields = [
    'date_of_birth',
    'scheduled_date',
    'last_worn_date',
    'purchase_date'
  ];
  
  // Convert timestamp fields
  timestampFields.forEach(field => {
    if (converted[field]) {
      converted[field] = pgTimestampToUnixTimestamp(converted[field]);
    }
  });
  
  // Convert date fields
  dateFields.forEach(field => {
    if (converted[field]) {
      converted[field] = pgDateToUnixTimestamp(converted[field]);
    }
  });
  
  return converted;
}

/**
 * Convert array of database rows to use Unix timestamps
 */
export function convertRowsTimestamps(rows: any[]): any[] {
  return rows.map(convertRowTimestamps);
}

/**
 * Validate Unix timestamp
 */
export function isValidUnixTimestamp(timestamp: number): boolean {
  // Check if it's a number and within reasonable bounds
  // Unix timestamp should be positive and not too far in the future
  const now = getCurrentUnixTimestamp();
  const maxFuture = now + (365 * 24 * 60 * 60 * 10); // 10 years in the future
  
  return (
    typeof timestamp === 'number' &&
    !isNaN(timestamp) &&
    timestamp > 0 &&
    timestamp <= maxFuture
  );
}

/**
 * Format Unix timestamp for logging/debugging
 */
export function formatUnixTimestamp(timestamp: number): string {
  return `${timestamp} (${unixTimestampToDate(timestamp).toISOString()})`;
}

/**
 * Calculate difference between two Unix timestamps in seconds
 */
export function timestampDifference(timestamp1: number, timestamp2: number): number {
  return Math.abs(timestamp1 - timestamp2);
}

/**
 * Check if a Unix timestamp represents today
 */
export function isToday(timestamp: number): boolean {
  const date = unixTimestampToDate(timestamp);
  const today = new Date();
  
  return (
    date.getFullYear() === today.getFullYear() &&
    date.getMonth() === today.getMonth() &&
    date.getDate() === today.getDate()
  );
}

/**
 * Check if a Unix timestamp represents a past date
 */
export function isPast(timestamp: number): boolean {
  const now = getCurrentUnixTimestamp();
  return timestamp < now;
}

/**
 * Check if a Unix timestamp represents a future date
 */
export function isFuture(timestamp: number): boolean {
  const now = getCurrentUnixTimestamp();
  return timestamp > now;
}

/**
 * Get Unix timestamp for start of day
 */
export function getStartOfDay(timestamp: number): number {
  const date = unixTimestampToDate(timestamp);
  date.setHours(0, 0, 0, 0);
  return dateToUnixTimestamp(date);
}

/**
 * Get Unix timestamp for end of day
 */
export function getEndOfDay(timestamp: number): number {
  const date = unixTimestampToDate(timestamp);
  date.setHours(23, 59, 59, 999);
  return dateToUnixTimestamp(date);
}

/**
 * Add days to Unix timestamp
 */
export function addDays(timestamp: number, days: number): number {
  const date = unixTimestampToDate(timestamp);
  date.setDate(date.getDate() + days);
  return dateToUnixTimestamp(date);
}

/**
 * Subtract days from Unix timestamp
 */
export function subtractDays(timestamp: number, days: number): number {
  return addDays(timestamp, -days);
}
